export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
}

export interface Policy {
  id: string;
  policyNumber: string;
  customerName: string;
  customerId: string;
  policyType: string;
  status: 'active' | 'inactive' | 'pending';
  faceAmount: number;
  premium: number;
  createdAt: Date;
}

export interface Scenario {
  id: string;
  name: string;
  policyId: string;
  asIsDetails: string;
  whatIfOptions: string[];
  category: 'face-amount' | 'premium' | 'income' | 'loan-repayment' | 'interest-rate' | 'policy-lapse' | 'as-is';
  data: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  rememberMe: boolean;
}

export interface SelectedCustomerData {
  name: string;
  policyNumber: string;
  customerId: string;
  details: {
    DOB: string;
    Email: string;
    Phone: string;
    Address: string;
    Occupation: string;
    "Annual Income": string;
    "Customer ID": string;
    "Policy Number": string;
    "Policy Type": string;
    Status: string;
  };
}

export interface SelectedPolicyData {
  id: string;
  name: string;
  description: string;
  coverage: string;
  premium: string;
  features: string[];
}

export interface DashboardState {
  activeTab: string;
  currentPolicy: Policy | null;
  scenarios: Scenario[];
  selectedScenarios: string[];
  selectedCustomerData: SelectedCustomerData | null;
  selectedPolicyData: SelectedPolicyData | null;
  loading: boolean;
  error: string | null;
}

// New policy-related types matching backend models
export interface PolicyDetails {
  id: string;
  name: string;
  description: string;
  coverage: string;
  premium: string;
  features: string[];
  issue_date: string;
  cash_value: string;
  payment_frequency: string;
  premium_type: string;
  next_due_date: string;
}

export interface PaymentHistory {
  date: string;
  amount: string;
  status: string;
}

export interface TransactionHistory {
  id: string;
  date: string;
  type: string;
  amount: string;
  remarks: string;
}

export interface ActiveRider {
  name: string;
  coverage: string;
  status: string;
}

export interface PolicyInfo {
  policy_number: string;
  policy_type: string;
  status: string;
  policy_details: PolicyDetails;
  payment_history: PaymentHistory[];
  transaction_history: TransactionHistory[];
  active_riders: ActiveRider[];
}

export interface Customer {
  customer_id: string;
  name: string;
  dob: string;
  email: string;
  phone: string;
  address: string;
  occupation: string;
  annual_income: string;
  policies: PolicyInfo[];
}

export interface PolicySearchRequest {
  customer_id?: string;
  policy_number?: string;
  customer_name?: string;
}

export interface PolicySearchResponse {
  customer: Customer | null;
  found: boolean;
  message: string;
}