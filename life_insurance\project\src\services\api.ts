/**
 * API Service Layer for Life Insurance Application
 * Handles all HTTP requests to the FastAPI backend server with SQLite3 database
 */

import { Scenario, PolicySearchRequest, PolicySearchResponse, Customer } from '../types';

// API Configuration
const API_BASE_URL = 'http://localhost:8000';
const API_ENDPOINTS = {
  health: '/health',
  scenarios: '/api/scenarios',
  selectedScenarios: '/api/scenarios/selected',
  policies: '/api/policies',
  policySearch: '/api/policies/search',
  customerPolicies: '/api/policies/customer',
} as const;

// Demo credentials (in production, this would come from secure auth)
const DEMO_CREDENTIALS = {
  username: 'demo',
  password: 'demo123',
};

/**
 * Create Basic Auth header
 */
function createAuthHeader(): string {
  const credentials = `${DEMO_CREDENTIALS.username}:${DEMO_CREDENTIALS.password}`;
  const encoded = btoa(credentials);
  return `Basic ${encoded}`;
}

/**
 * Generic API request function with error handling
 */
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'Authorization': createAuthHeader(),
  };

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof Error) {
      console.error(`API Request failed for ${endpoint}:`, error.message);
      throw error;
    }
    throw new Error(`Unknown error occurred while calling ${endpoint}`);
  }
}

/**
 * API Service Class
 */
export class ApiService {
  /**
   * Health check endpoint
   */
  static async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return apiRequest(API_ENDPOINTS.health, { method: 'GET' });
  }

  /**
   * Get all scenarios for the authenticated user
   */
  static async getScenarios(): Promise<Scenario[]> {
    const response = await apiRequest<{ scenarios: Scenario[] }>(
      API_ENDPOINTS.scenarios,
      { method: 'GET' }
    );
    
    // Convert date strings back to Date objects
    return response.scenarios.map(scenario => ({
      ...scenario,
      createdAt: new Date(scenario.createdAt),
      updatedAt: new Date(scenario.updatedAt),
    }));
  }

  /**
   * Create a new scenario
   */
  static async createScenario(scenarioData: Omit<Scenario, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    await apiRequest(API_ENDPOINTS.scenarios, {
      method: 'POST',
      body: JSON.stringify(scenarioData),
    });
  }

  /**
   * Update an existing scenario
   */
  static async updateScenario(
    scenarioId: string,
    updates: Partial<Omit<Scenario, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    await apiRequest(`${API_ENDPOINTS.scenarios}/${scenarioId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  /**
   * Delete a scenario
   */
  static async deleteScenario(scenarioId: string): Promise<void> {
    await apiRequest(`${API_ENDPOINTS.scenarios}/${scenarioId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Get selected scenario IDs for the authenticated user
   */
  static async getSelectedScenarios(): Promise<string[]> {
    const response = await apiRequest<{ selectedScenarios: string[] }>(
      API_ENDPOINTS.selectedScenarios,
      { method: 'GET' }
    );
    return response.selectedScenarios;
  }

  /**
   * Update selected scenario IDs for the authenticated user
   */
  static async updateSelectedScenarios(selectedIds: string[]): Promise<void> {
    await apiRequest(API_ENDPOINTS.selectedScenarios, {
      method: 'POST',
      body: JSON.stringify({ selectedScenarios: selectedIds }),
    });
  }

  /**
   * Batch operations for multiple scenarios
   */
  static async deleteMultipleScenarios(scenarioIds: string[]): Promise<void> {
    // Since the backend doesn't have a batch delete endpoint,
    // we'll delete them one by one
    const deletePromises = scenarioIds.map(id => this.deleteScenario(id));
    await Promise.all(deletePromises);
  }

  /**
   * Search for customer policies
   */
  static async searchPolicies(searchRequest: PolicySearchRequest): Promise<PolicySearchResponse> {
    return apiRequest<PolicySearchResponse>(API_ENDPOINTS.policySearch, {
      method: 'POST',
      body: JSON.stringify(searchRequest),
    });
  }

  /**
   * Get all policies for a specific customer
   */
  static async getCustomerPolicies(customerId: string): Promise<Customer> {
    return apiRequest<Customer>(`${API_ENDPOINTS.customerPolicies}/${customerId}`, {
      method: 'GET',
    });
  }
}

/**
 * API Error Types
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public endpoint?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Connection status checker
 */
export class ConnectionChecker {
  private static isOnline = true;
  private static listeners: Array<(online: boolean) => void> = [];

  static async checkConnection(): Promise<boolean> {
    try {
      await ApiService.healthCheck();
      this.setOnlineStatus(true);
      return true;
    } catch (error) {
      this.setOnlineStatus(false);
      return false;
    }
  }

  static addListener(callback: (online: boolean) => void): void {
    this.listeners.push(callback);
  }

  static removeListener(callback: (online: boolean) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  private static setOnlineStatus(online: boolean): void {
    if (this.isOnline !== online) {
      this.isOnline = online;
      this.listeners.forEach(listener => listener(online));
    }
  }

  static get online(): boolean {
    return this.isOnline;
  }
}

/**
 * Utility functions for API integration
 */
export const ApiUtils = {
  /**
   * Retry an API call with exponential backoff
   */
  async retryWithBackoff<T>(
    apiCall: () => Promise<T>,
    maxRetries = 3,
    baseDelay = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt === maxRetries) {
          break;
        }

        // Exponential backoff: 1s, 2s, 4s, etc.
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  },

  /**
   * Check if error is a network error
   */
  isNetworkError(error: Error): boolean {
    return error.message.includes('fetch') || 
           error.message.includes('network') ||
           error.message.includes('Failed to fetch');
  },

  /**
   * Format error message for user display
   */
  formatErrorMessage(error: Error): string {
    if (this.isNetworkError(error)) {
      return 'Unable to connect to server. Please check your connection and try again.';
    }
    
    if (error.message.includes('401')) {
      return 'Authentication failed. Please refresh the page and try again.';
    }
    
    if (error.message.includes('404')) {
      return 'The requested resource was not found.';
    }
    
    if (error.message.includes('500')) {
      return 'Server error occurred. Please try again later.';
    }
    
    return error.message || 'An unexpected error occurred.';
  }
};

export default ApiService;
