import React, { useState } from 'react';
import { ArrowRight, Calculator, Search, User, Shield, DollarSign, FileText, CreditCard, Activity } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import { ApiService } from '../../services/api';
import { Customer, PolicyInfo, PolicySearchRequest } from '../../types';

// Legacy interface for compatibility
interface Policy {
  id: string;
  name: string;
  description: string;
  coverage: string;
  premium: string;
  features: string[];
}

const PolicySelection = () => {
  const [customerName, setCustomerName] = useState('<PERSON>');
  const [policyNumber, setPolicyNumber] = useState('POL-12345678');
  const [customerId, setCustomerId] = useState('CUS-567890');
  const [searchClicked, setSearchClicked] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<PolicyInfo | null>(null);
  const [currentCustomer, setCurrentCustomer] = useState<Customer | null>(null);
  const [selectedPolicyIndex, setSelectedPolicyIndex] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setActiveTab, setSelectedCustomerData, setSelectedPolicyData } = useDashboard();

  // Debug logging
  console.log('🏠 PolicySelection render:', {
    searchClicked,
    loading,
    error,
    currentCustomer: currentCustomer?.name || 'null',
    selectedPolicy: selectedPolicy?.policy_number || 'null'
  });

  // Helper: localStorage key
  const getStorageKey = (id: string, policy: string, name: string) => `customerData:${id.trim()}|${policy.trim()}|${name.trim()}`;

  // Save details to localStorage on successful search
  const saveToLocalStorage = (id: string, policy: string, name: string) => {
    const key = getStorageKey(id, policy, name);
    localStorage.setItem(key, JSON.stringify({ id, policy, name }));
  };

  // Try to auto-fill other fields if a match is found in localStorage
  const tryAutoFill = (changedField: 'id' | 'policy' | 'name', value: string) => {
    // Only auto-fill if the other fields are empty
    let found = false;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('customerData:')) {
        const data = JSON.parse(localStorage.getItem(key) || '{}');
        if (
          (changedField === 'id' && data.id === value && !policyNumber && !customerName) ||
          (changedField === 'policy' && data.policy === value && !customerId && !customerName) ||
          (changedField === 'name' && data.name === value && !customerId && !policyNumber)
        ) {
          if (changedField !== 'id') setCustomerId(data.id);
          if (changedField !== 'policy') setPolicyNumber(data.policy);
          if (changedField !== 'name') setCustomerName(data.name);
          found = true;
          break;
        }
      }
    }
    return found;
  };

  const handleSearch = async () => {
    console.log('🔍 Starting policy search...');
    setSearchClicked(true);
    setLoading(true);
    setError(null);

    try {
      const searchRequest: PolicySearchRequest = {
        customer_id: customerId.trim() || undefined,
        policy_number: policyNumber.trim() || undefined,
        customer_name: customerName.trim() || undefined,
      };

      console.log('📤 Search request:', searchRequest);
      const response = await ApiService.searchPolicies(searchRequest);
      console.log('📥 Search response:', response);

      if (response.found && response.customer) {
        console.log('✅ Customer found:', response.customer.name);
        setCurrentCustomer(response.customer);
        saveToLocalStorage(customerId, policyNumber, customerName);
      } else {
        console.log('❌ Customer not found:', response.message);
        setCurrentCustomer(null);
        setError(response.message);
      }
    } catch (err) {
      console.error('💥 Error searching policies:', err);
      setError('Failed to search policies. Please try again.');
      setCurrentCustomer(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPolicy = (policy: PolicyInfo, index: number) => {
    setSelectedPolicy(policy);
    setSelectedPolicyIndex(index);
  };

  // Test function to verify API connection
  const testApiConnection = async () => {
    try {
      console.log('🧪 Testing API connection...');
      const response = await ApiService.searchPolicies({
        customer_id: "CUS-567890",
        policy_number: "POL-12345678",
        customer_name: "John Smith"
      });
      console.log('🧪 Test API response:', response);
      alert(`API Test: ${response.found ? 'SUCCESS' : 'FAILED'} - ${response.message}`);
    } catch (error) {
      console.error('🧪 Test API error:', error);
      alert(`API Test FAILED: ${error}`);
    }
  };

  const renderSearchSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <h2 className="text-xl font-bold text-black mb-6 pb-3 border-b border-gray-200">
        Search Your Policy
      </h2>
      <div className="mb-4">
        <button
          onClick={testApiConnection}
          className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-sm"
        >
          🧪 Test API Connection
        </button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Customer ID</label>
          <input
            type="text"
            value={customerId}
            onChange={(e) => {
              const value = e.target.value;
              setCustomerId(value);
              // Try to auto-fill from localStorage
              if (value) tryAutoFill('id', value);
            }}
            placeholder="Enter customer ID"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Policy Number</label>
          <input
            type="text"
            value={policyNumber}
            onChange={(e) => {
              setPolicyNumber(e.target.value);
              if (e.target.value) tryAutoFill('policy', e.target.value);
            }}
            placeholder="Enter policy number"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Customer Name</label>
          <input
            type="text"
            value={customerName}
            onChange={(e) => {
              setCustomerName(e.target.value);
              if (e.target.value) tryAutoFill('name', e.target.value);
            }}
            placeholder="Enter customer name"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="flex items-end">
          <button
            onClick={handleSearch}
            disabled={loading}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Search className="w-4 h-4" />
            )}
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>
      </div>
    </div>
  );

  const renderAvailablePolicies = () => {
    if (!currentCustomer) return null;
    const availablePolicies = currentCustomer.policies || [];
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-black">Your Policies</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Policy Number
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sum Assurance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                 Annual Premium
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  View
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {availablePolicies.map((policy: PolicyInfo, index: number) => {
                const statusClass = policy.status === "Active"
                  ? "inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"
                  : "inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800";
                return (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {policy.policy_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={statusClass}>{policy.status}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.policy_details.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 text-left">
                      {policy.policy_details.coverage.includes("$") && !policy.policy_details.coverage.includes("%")
                        ? `$${policy.policy_details.coverage.replace(/\s*\$/g, "")}`
                        : policy.policy_details.coverage}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {policy.policy_details.premium}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleSelectPolicy(policy, index)}
                        className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors"
                      >
                        Select
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderSelectedPolicyDetails = () => {
    if (!selectedPolicy || !currentCustomer) return null;
    return (
      <div className="space-y-8">
        <h2 className="text-xl font-semibold text-gray-900 pb-3 border-b border-gray-200">
          Selected Policy Details
        </h2>
        {/* Customer and Policy Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <User className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Customer Information</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Full Name</span>
                <span className="text-sm text-gray-900">{currentCustomer.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Date of Birth</span>
                <span className="text-sm text-gray-900">{currentCustomer.dob}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Customer ID</span>
                <span className="text-sm text-gray-900">{currentCustomer.customer_id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Email</span>
                <span className="text-sm text-gray-900">{currentCustomer.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Phone</span>
                <span className="text-sm text-gray-900">{currentCustomer.phone}</span>
              </div>
            </div>
          </div>
          {/* Coverage Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Coverage Information</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Policy Type</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Face Amount</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.coverage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.premium}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium Type</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.premium_type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Payment Frequency</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.payment_frequency}</span>
              </div>
            </div>
          </div>
          {/* Policy Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Policy Details</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Policy Number</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_number}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Issue Date</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.issue_date}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Status</span>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  selectedPolicy.status === 'Active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {selectedPolicy.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Type</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.premium}</span>
              </div>
            </div>
          </div>
          {/* Financial Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center gap-2 mb-4">
              <DollarSign className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-bold text-black">Financial Details</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Face Amount</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.coverage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Cash Value</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.cash_value}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Premium</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.premium}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Payment Frequency</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.payment_frequency}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Next Due Date</span>
                <span className="text-sm text-gray-900">{selectedPolicy.policy_details.next_due_date}</span>
              </div>
            </div>
          </div>
        </div>
        {/* Payment History and Riders */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Payment History */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Payment History</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {selectedPolicy.payment_history.map((payment, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{payment.amount}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          payment.status === 'Paid'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {payment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          {/* Active Riders */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-600" />
                <h3 className="text-lg font-bold text-black">Active Riders</h3>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Rider</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Coverage</th>
                    <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {selectedPolicy.active_riders.length > 0 ? (
                    selectedPolicy.active_riders.map((rider, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{rider.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{rider.coverage}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            rider.status === 'Active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {rider.status}
                          </span>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                        No active riders
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        {/* Transaction History */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-bold text-black">Transaction History</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Transaction ID</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Amount</th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase">Remarks</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {selectedPolicy.transaction_history.length > 0 ? (
                  selectedPolicy.transaction_history.map((transaction, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.id}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.type}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${transaction.amount}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.remarks}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      No transaction history available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  const renderNoResultsFound = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="text-center">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
          <p className="text-red-800">{error || "Customer not found. Please check your details."}</p>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-blue-800 font-medium mb-2">Sample Test Data:</p>
          <div className="text-blue-700 text-sm space-y-1">
            <p>• CUS-567890 | POL-12345678 | John Smith</p>
            <p>• CUS-123456 | POL-87654321 | Jane Doe</p>
            <p>• CUS-111111 | POL-11111111 | Michael Johnson</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLoadingState = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Searching for customer policies...</p>
      </div>
    </div>
  );

  const renderIllustrationSection = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <h3 className="text-lg font-bold text-black mb-4">Ready to proceed?</h3>
      <p className="text-gray-600 mb-6">Generate detailed policy illustrations and projections</p>
      <button
        className="bg-blue-600 text-white px-8 py-3 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 mx-auto"
        onClick={() => {
          if (selectedPolicy && currentCustomer) {
            // Save selected customer and policy data to context
            setSelectedCustomerData({
              name: currentCustomer.name,
              policyNumber: selectedPolicy.policy_number,
              customerId: currentCustomer.customer_id,
              details: {
                DOB: currentCustomer.dob,
                Email: currentCustomer.email,
                Phone: currentCustomer.phone,
                Address: currentCustomer.address,
                Occupation: currentCustomer.occupation,
                "Annual Income": currentCustomer.annual_income,
                "Customer ID": currentCustomer.customer_id,
                "Policy Number": selectedPolicy.policy_number,
                "Policy Type": selectedPolicy.policy_type,
                Status: selectedPolicy.status,
              }
            });

            setSelectedPolicyData({
              id: selectedPolicy.policy_details.id,
              name: selectedPolicy.policy_details.name,
              description: selectedPolicy.policy_details.description,
              coverage: selectedPolicy.policy_details.coverage,
              premium: selectedPolicy.policy_details.premium,
              features: selectedPolicy.policy_details.features
            });

            setActiveTab('as-is');
          }
        }}
        disabled={!selectedPolicy || !currentCustomer}
      >
        <Calculator className="w-5 h-5" />
        📊 Go to Illustration
      </button>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Debug Info */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 text-sm">
        <p><strong>🐛 Debug Info:</strong></p>
        <p>Component rendered at: {new Date().toLocaleTimeString()}</p>
        <p>Search clicked: {searchClicked ? 'Yes' : 'No'}</p>
        <p>Loading: {loading ? 'Yes' : 'No'}</p>
        <p>Current customer: {currentCustomer?.name || 'None'}</p>
        <p>Error: {error || 'None'}</p>
      </div>

      {/* Main Title */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Known Your Policy</h1>
        <p className="text-gray-600 dark:text-gray-400">Enter customer details to search and select a policy for illustration.</p>
      </div>
      {/* Search Section */}
      {renderSearchSection()}
      {/* Search Results */}
      {searchClicked && (
        <>
          {loading ? (
            renderLoadingState()
          ) : currentCustomer ? (
            <>
              {renderAvailablePolicies()}
              {selectedPolicy && renderSelectedPolicyDetails()}
            </>
          ) : (
            renderNoResultsFound()
          )}
        </>
      )}
      {/* Info Message when no search performed */}
      {!searchClicked && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <p className="text-blue-800">Enter customer details and click Search to view available policies.</p>
        </div>
      )}
      {/* Illustration Section */}
      {renderIllustrationSection()}
    </div>
  );
};

export default PolicySelection;