{"customers": {"CUS-567890": {"customer_id": "CUS-567890", "name": "<PERSON>", "dob": "05.02.1994", "email": "<EMAIL>", "phone": "(*************", "address": "123 Main Street, New York, NY 10001", "occupation": "Software Engineer", "annual_income": "$85,000", "policies": [{"policy_number": "POL-12345678", "policy_type": "Whole Life Insurance", "status": "Active", "policy_details": {"id": "POL-12345678", "name": "Whole Life Insurance", "description": "Permanent life insurance with cash value accumulation", "coverage": "500,000 $", "premium": "2000 $ annually", "features": ["Cash Value Growth", "Tax Benefits", "Loan Options", "Guaranteed Death Benefit"], "issue_date": "01/01/2024", "cash_value": "$25,000", "payment_frequency": "Monthly", "premium_type": "Level Premium", "next_due_date": "01/01/2024"}, "payment_history": [{"date": "01/15/2024", "amount": "$200", "status": "Paid"}, {"date": "12/15/2023", "amount": "$200", "status": "Paid"}, {"date": "11/15/2023", "amount": "$200", "status": "Paid"}], "transaction_history": [{"id": "TNX 001", "date": "03-01-2024", "type": "Premium Payment", "amount": "1200.00", "remarks": "Increase Cash Value"}, {"id": "TNX 002", "date": "04-27-2022", "type": "Loan Repayment", "amount": "2349.00", "remarks": "Paid up additions"}], "active_riders": [{"name": "Critical Illness Rider", "coverage": "$50,000", "status": "Active"}, {"name": "Accidental Death Benefit", "coverage": "$100,000", "status": "Active"}]}, {"policy_number": "POL-12345679", "policy_type": "Term Life Insurance", "status": "Active", "policy_details": {"id": "POL-12345679", "name": "Term Life Insurance", "description": "Affordable temporary life insurance coverage", "coverage": "750,000 $", "premium": "800 $ annually", "features": ["Lower Premiums", "Convertible Option", "Level Premiums", "Renewable"], "issue_date": "01/01/2023", "cash_value": "$0", "payment_frequency": "Monthly", "premium_type": "Level Premium", "next_due_date": "01/01/2024"}, "payment_history": [{"date": "01/15/2024", "amount": "$67", "status": "Paid"}, {"date": "12/15/2023", "amount": "$67", "status": "Paid"}], "transaction_history": [{"id": "TNX 003", "date": "01-15-2024", "type": "Premium Payment", "amount": "67.00", "remarks": "Monthly Premium"}], "active_riders": []}, {"policy_number": "POL-12345680", "policy_type": "Universal Life Insurance", "status": "Inactive", "policy_details": {"id": "POL-12345680", "name": "Universal Life Insurance", "description": "Flexible premium permanent life insurance", "coverage": "600,000 $", "premium": "1500 $ annually", "features": ["Flexible Premiums", "Investment Component", "Adjustable Death Benefit", "Tax Advantages"], "issue_date": "01/01/2022", "cash_value": "$15,000", "payment_frequency": "Monthly", "premium_type": "Flexible Premium", "next_due_date": "01/01/2024"}, "payment_history": [], "transaction_history": [], "active_riders": []}, {"policy_number": "POL-12345681", "policy_type": "Disability Insurance", "status": "Inactive", "policy_details": {"id": "POL-12345681", "name": "Disability Insurance", "description": "Income protection in case of disability", "coverage": "60% of income", "premium": "1200 $ annually", "features": ["Income Replacement", "Short & Long Term", "Partial Benefits", "Cost of Living Adjustments"], "issue_date": "01/01/2022", "cash_value": "$0", "payment_frequency": "Monthly", "premium_type": "Level Premium", "next_due_date": "01/01/2024"}, "payment_history": [], "transaction_history": [], "active_riders": []}, {"policy_number": "POL-12345682", "policy_type": "Critical Illness Insurance", "status": "Inactive", "policy_details": {"id": "POL-12345682", "name": "Critical Illness Insurance", "description": "Lump sum payment for critical illness diagnosis", "coverage": "200,000 $", "premium": "600 $ annually", "features": ["Lump Sum Payment", "Multiple Conditions Covered", "No Restrictions on Use", "Return of Premium Option"], "issue_date": "01/01/2022", "cash_value": "$0", "payment_frequency": "Monthly", "premium_type": "Level Premium", "next_due_date": "01/01/2024"}, "payment_history": [], "transaction_history": [], "active_riders": []}]}, "CUS-123456": {"customer_id": "CUS-123456", "name": "<PERSON>", "dob": "12.08.1988", "email": "<EMAIL>", "phone": "(*************", "address": "456 Oak Avenue, Los Angeles, CA 90210", "occupation": "Marketing Manager", "annual_income": "$75,000", "policies": [{"policy_number": "POL-87654321", "policy_type": "Term Life Insurance", "status": "Active", "policy_details": {"id": "POL-87654321", "name": "Term Life Insurance", "description": "Affordable temporary life insurance coverage", "coverage": "750,000 $", "premium": "150 $ monthly", "features": ["Affordable Premiums", "Convertible", "Level Death Benefit", "Renewable"], "issue_date": "01/01/2023", "cash_value": "$0", "payment_frequency": "Monthly", "premium_type": "Level Premium", "next_due_date": "01/01/2024"}, "payment_history": [{"date": "01/15/2024", "amount": "$150", "status": "Paid"}, {"date": "12/15/2023", "amount": "$150", "status": "Paid"}], "transaction_history": [{"id": "TNX 004", "date": "01-15-2024", "type": "Premium Payment", "amount": "150.00", "remarks": "Monthly Premium"}], "active_riders": []}, {"policy_number": "POL-87654322", "policy_type": "Whole Life Insurance", "status": "Active", "policy_details": {"id": "POL-87654322", "name": "Whole Life Insurance", "description": "Permanent life insurance with guaranteed cash value", "coverage": "500,000 $", "premium": "300 $ monthly", "features": ["Guaranteed Cash Value", "Dividends", "Loan Options", "Tax Benefits"], "issue_date": "01/01/2022", "cash_value": "$18,000", "payment_frequency": "Monthly", "premium_type": "Level Premium", "next_due_date": "01/01/2024"}, "payment_history": [{"date": "01/15/2024", "amount": "$300", "status": "Paid"}, {"date": "12/15/2023", "amount": "$300", "status": "Paid"}], "transaction_history": [{"id": "TNX 005", "date": "01-15-2024", "type": "Premium Payment", "amount": "300.00", "remarks": "Monthly Premium"}], "active_riders": [{"name": "Waiver of Premium", "coverage": "Premium Amount", "status": "Active"}]}, {"policy_number": "POL-87654323", "policy_type": "Health Insurance", "status": "Inactive", "policy_details": {"id": "POL-87654323", "name": "Health Insurance", "description": "Comprehensive medical coverage", "coverage": "Unlimited", "premium": "450 $ monthly", "features": ["Preventive Care", "Prescription Coverage", "Specialist Access", "Emergency Care"], "issue_date": "01/01/2021", "cash_value": "$0", "payment_frequency": "Monthly", "premium_type": "Level Premium", "next_due_date": "01/01/2024"}, "payment_history": [], "transaction_history": [], "active_riders": []}, {"policy_number": "POL-87654324", "policy_type": "Auto Insurance", "status": "Inactive", "policy_details": {"id": "POL-87654324", "name": "Auto Insurance", "description": "Complete vehicle protection coverage", "coverage": "500,000 $ liability", "premium": "120 $ monthly", "features": ["Collision Coverage", "Comprehensive", "Liability Protection", "Roadside Assistance"], "issue_date": "01/01/2021", "cash_value": "$0", "payment_frequency": "Monthly", "premium_type": "Level Premium", "next_due_date": "01/01/2024"}, "payment_history": [], "transaction_history": [], "active_riders": []}]}, "CUS-111111": {"customer_id": "CUS-111111", "name": "<PERSON>", "dob": "22.11.1985", "email": "micha<PERSON>.<EMAIL>", "phone": "(*************", "address": "789 Pine Street, Chicago, IL 60601", "occupation": "Financial Advisor", "annual_income": "$95,000", "policies": [{"policy_number": "POL-11111111", "policy_type": "Investment Life Insurance", "status": "Active", "policy_details": {"id": "POL-11111111", "name": "Investment Life Insurance", "description": "Life insurance with investment opportunities", "coverage": "800,000 $", "premium": "1500 $ quarterly", "features": ["Investment Growth", "Market Linked Returns", "Flexible Premiums", "Tax Efficiency"], "issue_date": "01/01/2020", "cash_value": "$45,000", "payment_frequency": "Quarterly", "premium_type": "Flexible Premium", "next_due_date": "04/01/2024"}, "payment_history": [{"date": "01/15/2024", "amount": "$1500", "status": "Paid"}, {"date": "10/15/2023", "amount": "$1500", "status": "Paid"}], "transaction_history": [{"id": "TNX 006", "date": "01-15-2024", "type": "Premium Payment", "amount": "1500.00", "remarks": "Quarterly Premium"}, {"id": "TNX 007", "date": "12-01-2023", "type": "Investment Gain", "amount": "2500.00", "remarks": "Market Performance"}], "active_riders": [{"name": "Investment Rider", "coverage": "Variable", "status": "Active"}]}, {"policy_number": "POL-11111112", "policy_type": "Retirement Annuity", "status": "Active", "policy_details": {"id": "POL-11111112", "name": "Retirement Annuity", "description": "Guaranteed income for retirement", "coverage": "Lifetime Income", "premium": "2000 $ quarterly", "features": ["Guaranteed Income", "Inflation Protection", "Death Benefits", "Tax Deferred Growth"], "issue_date": "01/01/2019", "cash_value": "$65,000", "payment_frequency": "Quarterly", "premium_type": "Level Premium", "next_due_date": "04/01/2024"}, "payment_history": [{"date": "01/15/2024", "amount": "$2000", "status": "Paid"}, {"date": "10/15/2023", "amount": "$2000", "status": "Paid"}], "transaction_history": [{"id": "TNX 008", "date": "01-15-2024", "type": "Premium Payment", "amount": "2000.00", "remarks": "Quarterly Premium"}], "active_riders": [{"name": "Inflation Protection Rider", "coverage": "3% Annual", "status": "Active"}]}, {"policy_number": "POL-11111113", "policy_type": "Family Protection Plan", "status": "Inactive", "policy_details": {"id": "POL-11111113", "name": "Family Protection Plan", "description": "Comprehensive family coverage", "coverage": "1,000,000 $", "premium": "800 $ quarterly", "features": ["Family Coverage", "Child Benefits", "Spouse Protection", "Education Fund"], "issue_date": "01/01/2018", "cash_value": "$25,000", "payment_frequency": "Quarterly", "premium_type": "Level Premium", "next_due_date": "04/01/2024"}, "payment_history": [], "transaction_history": [], "active_riders": []}, {"policy_number": "POL-11111114", "policy_type": "Business Insurance", "status": "Inactive", "policy_details": {"id": "POL-11111114", "name": "Business Insurance", "description": "Key person and business protection", "coverage": "2,000,000 $", "premium": "3000 $ quarterly", "features": ["Key Person Coverage", "Business Loan Protection", "Buy-Sell Agreements", "Tax Benefits"], "issue_date": "01/01/2017", "cash_value": "$0", "payment_frequency": "Quarterly", "premium_type": "Level Premium", "next_due_date": "04/01/2024"}, "payment_history": [], "transaction_history": [], "active_riders": []}]}}}